import API from '@/services/API';
import { notification } from '@/services';
import BulkImport from './bulkImport';
import EmployeeSearch from './getAllEmployees';

const notify = notification();

// Helper function to transform data for API (basic operations)
const transformEmployeeData = (employee) => {
  return {
    FirstName: employee.FirstName || '',
    LastName: employee.LastName || '',
    PersonalEmail: employee.PersonalEmail || '',
    WorkEmail: employee.WorkEmail || '',
    PhoneNumber: employee.PhoneNumber || '',
    Address: employee.Address || '',
    EmergencyContact: employee.EmergencyContact || '',
    EmergencyContactNumber: employee.EmergencyContactNumber || '',
    DateOfBirth: employee.DateOfBirth ? new Date(employee.DateOfBirth).toISOString() : null,
    Gender: employee.Gender || '',
    DateOfJoining: employee.DateOfJoining ? new Date(employee.DateOfJoining).toISOString() : null,
    // OBStatus: employee.OBStatus === '1' || employee.OBStatus === true,
    OBStatus: false,
    // IsActive: employee.IsActive === '1' || employee.IsActive === true,
    IsActive:true,
    ProfilePicture: employee.ProfilePicture || '',
    DeptID: parseInt(employee.DeptID) || 0,
    PositionID: parseInt(employee.PositionID) || 0,
    EmpType: employee.EmpType || ''
  };
};

// Get single employee by ID
const getEmployeeById = async (id) => {
  try {
    const response = await API.get(`/Employees/${id}`);
    return response.data;
  } catch (error) {
    notify.error('Failed to fetch employee: ' + error.message);
    return null;
  }
};

// Create a new employee
const createEmployee = async (employeeData) => {
  try {
    const transformedData = transformEmployeeData(employeeData);
    const response = await API.post('/Employees', transformedData);
    notify.success('Employee created successfully');
    return response.data;
  } catch (error) {
    notify.error('Failed to create employee: ' + error.message);
    return null;
  }
};

// Update an employee
const updateEmployee = async (id, updatedData) => {
  try {
    const transformedData = transformEmployeeData(updatedData);
    const response = await API.put(`/Employees/${id}`, transformedData);
    notify.success('Employee updated successfully');
    return response.data;
  } catch (error) {
    notify.error('Failed to update employee: ' + error.message);
    return null;
  }
};

// Delete an employee
const deleteEmployee = async (id) => {
  try {
    await API.delete(`/Employees/${id}`);
    notify.success('Employee deleted successfully');
    return true;
  } catch (error) {
    notify.error('Failed to delete employee: ' + error.message);
    return false;
  }
};

const Employees = {
  // Basic CRUD operations
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  
  // Search operations (delegated to EmployeeSearch service)
  getAllEmployees: EmployeeSearch.fetchAllEmployees,
  searchEmployees: EmployeeSearch.searchEmployeesWithFilters,
  advancedSearch: EmployeeSearch.advancedEmployeeSearch,
  getAllEmployeesComplete: EmployeeSearch.fetchAllEmployeesComplete,
  searchByDepartment: EmployeeSearch.searchEmployeesByDepartment,
  searchByStatus: EmployeeSearch.searchEmployeesByStatus,
  
  // Bulk import operations (delegated to BulkImport service)
  bulkImportEmployees: BulkImport.bulkImportEmployees,
  validateEmployeeData: BulkImport.validateEmployeeData,
  previewImportData: BulkImport.previewImportData
};

export default Employees;