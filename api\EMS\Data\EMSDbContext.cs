﻿using Microsoft.EntityFrameworkCore;
using EMS.Models;

namespace EMS.Data
{
    public class EMSDbContext : DbContext
    {
        public EMSDbContext(DbContextOptions<EMSDbContext> options) : base(options)
        {

        }

        public DbSet<Employee> Employees { get; set; } 
        public DbSet<EmpAcadDetail> EmpAcadDetails { get; set; }
        public DbSet<EmpProfDetail> EmpProfDetails { get; set; }
        public DbSet<Position> Positions { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<EmpBankDetail> EmpBankDetails { get; set; }
        public DbSet<LnABalance> LnABalances { get; set; }
        public DbSet<LnA> LnAs { get; set; }
        public DbSet<LnATransaction> LnATransactions { get; set; }
        public DbSet<MonthlyPayrollTransaction> MonthlyPayrollTransactions { get; set; }
        public DbSet<EmpPayroll> EmpPayrolls { get; set; }
        public DbSet<SalaryStructure> SalaryStructures { get; set; }
        public DbSet<Leave> Leaves { get; set; }
        public DbSet<Attendance> Attendances { get; set; }
        public DbSet<EmpDeptPos> EmpDeptPositions { get; set; }
        public DbSet<EmpCred> EmpCreds { get; set; }
        public DbSet<EmpAssets> EmpAssets { get; set; }
        public DbSet<EmpUnderTaking> EmpUnderTakings { get; set; }
        public DbSet<EmpDoc> EmpDocs { get; set; }
        public DbSet<Role> Roles { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            // Additional model configurations can be added here
        }
    }

}
