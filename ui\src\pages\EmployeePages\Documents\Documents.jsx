import React, { useState } from "react";
import { motion } from "framer-motion";
import { Upload, Eye, X, Info } from "lucide-react";

const DOCUMENT_FIELDS = [
  { key: "aadhar", label: "Aadhar Card", required: true },
  { key: "pan", label: "PAN Card" },
  { key: "tenth", label: "10th Certificate" },
  { key: "twelfth", label: "12th Certificate" },
  { key: "graduation", label: "Graduation Certificate" },
  { key: "pg", label: "Post Graduation Certificate" }
];

const MAX_FILE_SIZE_MB = 5;

const Documents = () => {
  const [documents, setDocuments] = useState(
    DOCUMENT_FIELDS.reduce((acc, { key }) => {
      acc[key] = { docNumber: "", file: null, preview: null };
      return acc;
    }, {})
  );
  const [errors, setErrors] = useState({});
  const [previewModal, setPreviewModal] = useState({ isOpen: false, src: null, type: null });
  const [success, setSuccess] = useState("");

  const handleDocNumberChange = (key, value) => {
    setDocuments(prev => ({ ...prev, [key]: { ...prev[key], docNumber: value } }));
    setErrors(prev => ({ ...prev, [`${key}DocNumber`]: "" }));
  };

  const handleFileChange = (key, file) => {
    if (!file) return;
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      setErrors(prev => ({ ...prev, [`${key}File`]: `File size exceeds ${MAX_FILE_SIZE_MB} MB` }));
      return;
    }
    const preview = URL.createObjectURL(file);
    setDocuments(prev => ({ ...prev, [key]: { ...prev[key], file, preview } }));
    setErrors(prev => ({ ...prev, [`${key}File`]: "" }));
  };
  
  const removeFile = (key) => setDocuments(prev => ({ ...prev, [key]: { ...prev[key], file: null, preview: null } }));
  const validate = () => {
    const errs = {};
    DOCUMENT_FIELDS.forEach(({ key, label, required }) => {
      if (required) {
        if (!documents[key].docNumber.trim()) errs[`${key}DocNumber`] = `${label} number is required`;
        if (!documents[key].file) errs[`${key}File`] = `${label} document is required`;
      }
    });
    setErrors(errs);
    return Object.keys(errs).length === 0;
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validate()) return;
    setSuccess("Documents uploaded successfully!");
    setTimeout(() => setSuccess(""), 3500);
  };

  const closePreview = () => setPreviewModal({ isOpen: false, src: null, type: null });

  return (
    <div className="min-h-screen bg-gray-50 pt-5 px-3 md:px-6">
      <motion.div
        initial={{ opacity: 0, y: 8 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="max-w-5xl mx-auto bg-white rounded-xl shadow-md p-5 md:p-7"
      >
        <h1 className="text-2xl font-semibold text-slate-900 mb-6 md:mb-7">
          Upload Your Documents
        </h1>

        {success && (
          <div className="mb-4 flex items-center gap-2 rounded-lg bg-green-100 px-3 py-1.5 text-green-800 font-semibold text-sm">
            <CheckCircle className="w-5 h-5" />
            <span>{success}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6 md:space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 md:gap-y-6">
            {DOCUMENT_FIELDS.map(({ key, label, required }) => {
              const doc = documents[key];
              return (
                <div key={key} className="border border-slate-200 rounded-lg p-3 bg-slate-50 flex flex-col gap-1">
                  <div className="flex justify-between items-center mb-1">
                    <span className="font-semibold text-slate-800 text-sm">{label} {required && <span className="text-red-500">*</span>}</span>
                    {doc.file && (
                      <button
                        type="button"
                        title="Preview"
                        onClick={() => setPreviewModal({ isOpen: true, src: doc.preview, type: doc.file.type })}
                        className="text-blue-600 hover:text-blue-700 p-1"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  <div className="flex items-center gap-2 mb-1">
                    <input
                      type="text"
                      value={doc.docNumber}
                      onChange={(e) => handleDocNumberChange(key, e.target.value)}
                      placeholder="Doc Number"
                      className={`rounded-lg border px-3 py-1.5 text-xs text-slate-900 bg-white w-full ${errors[`${key}DocNumber`] ? "border-red-500" : "border-slate-300"}`}
                    />
                    {errors[`${key}DocNumber`] && (
                      <span title={errors[`${key}DocNumber`]} className="text-red-500">
                        <Info className="w-3 h-3" />
                      </span>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <input
                      id={`file-upload-${key}`}
                      type="file"
                      accept="image/*,application/pdf"
                      onChange={(e) => handleFileChange(key, e.target.files[0])}
                      className="hidden"
                    />
                    <label
                      htmlFor={`file-upload-${key}`}
                      className="cursor-pointer inline-flex items-center gap-1 rounded-md bg-blue-600 hover:bg-blue-700 text-xs text-white font-semibold px-3 py-1.5 select-none transition"
                      title="Choose file"
                    >
                      <Upload className="w-4 h-4" />
                      {doc.file ? "Change" : "Upload"}
                    </label>

                    {doc.file && (
                      <>
                        <span className="truncate text-xs font-medium text-slate-700 max-w-[100px]">
                          {doc.file.name}
                        </span>
                        <button
                          type="button"
                          onClick={() => removeFile(key)}
                          className="text-red-500 hover:text-red-700"
                          title="Remove uploaded file"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    )}

                    {errors[`${key}File`] && (
                      <span title={errors[`${key}File`]} className="text-red-500">
                        <Info className="w-3 h-3" />
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
          <button
            type="submit"
            className="w-full rounded-lg bg-blue-600 py-2 text-white font-semibold text-sm hover:bg-blue-700 transition"
          >
            Save Documents
          </button>
        </form>
      </motion.div>

      {/* Preview Modal */}
      {previewModal.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-3">
          <div className="bg-white rounded-xl shadow-lg max-w-2xl w-full p-3 relative overflow-auto max-h-[70vh]">
            <button
              onClick={closePreview}
              aria-label="Close preview"
              className="absolute top-2 right-2 bg-gray-100 hover:bg-gray-200 rounded-lg p-1"
            >
              <X className="w-5 h-5 text-gray-700" />
            </button>
            {previewModal.type && previewModal.type.startsWith("image/") ? (
              <img
                src={previewModal.src}
                alt="Document preview"
                className="w-full h-[60vh] object-contain rounded"
              />
            ) : (
              <iframe
                title="Document Preview"
                src={previewModal.src}
                className="w-full h-[60vh] rounded border-none"
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Documents;
