﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EMS.Models
{
    public class MonthlyPayrollTransaction
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int MptID { get; set; }

        public int EmpID { get; set; }

        public string Month { get; set; } // e.g., "January"

        public int Year { get; set; }

        public double? BasicSalary { get; set; }

        public double? HRA { get; set; }

        public double? DA { get; set; }

        public double? OtherAllowances { get; set; }

        public double? Deductions { get; set; }

        public double? NetPay { get; set; }

        public DateTime TransactionDate { get; set; } = DateTime.Now;

    }
}
