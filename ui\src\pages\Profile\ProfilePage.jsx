"use client";
import React, { useEffect } from "react";
import useEmpDataStore from '@/store/empDataStore';
import useAuthStore from '@/store/authStore';

function ProfilePage() {
  // Auth store
  const { user } = useAuthStore();
  
  // Employee data store
  const { 
    currentEmployee, 
    fetchEmployeeById, 
    loading: empLoading, 
    error: empError 
  } = useEmpDataStore();

  const [isEditing, setIsEditing] = React.useState(false);
  const [editData, setEditData] = React.useState({});

  // Fetch employee data when component mounts
  useEffect(() => {
    if (user?.empId && !currentEmployee) {
      fetchEmployeeById(user.empId);
    }
  }, [user?.empId, currentEmployee, fetchEmployeeById]);

  // Map employee data for display
  const employee = currentEmployee ? {
    EmpID: currentEmployee.EmpID,
    FirstName: currentEmployee.FirstName || '',
    LastName: currentEmployee.LastName || '',
    PersonalEmail: currentEmployee.PersonalEmail || '',
    WorkEmail: currentEmployee.WorkEmail || '',
    PhoneNumber: currentEmployee.PhoneNumber || '',
    Address: currentEmployee.Address || '',
    EmergencyContact: currentEmployee.EmergencyContact || '',
    EmergencyContactNumber: currentEmployee.EmergencyContactNumber || '',
    DateOfBirth: currentEmployee.DateOfBirth || '',
    Gender: currentEmployee.Gender || '',
    DateOfJoining: currentEmployee.DateOfJoining || '',
    OBStatus: currentEmployee.OBStatus || false,
    IsActive: currentEmployee.IsActive || false,
    ProfilePicture: currentEmployee.ProfilePicture || '',
    Signature: currentEmployee.Signature || null,
    RoleID: currentEmployee.RoleID || 0,
  } : null;

  // Format dates
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format date for input
  const formatDateForInput = (dateString) => {
    return new Date(dateString).toISOString().split("T")[0];
  };

  // Calculate age
  const calculateAge = (birthDate) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }
    return age;
  };

  // Handle edit mode
  const handleEdit = () => {
    if (employee) {
      setEditData({ ...employee });
      setIsEditing(true);
    }
  };

  // Handle save
  const handleSave = () => {
    // TODO: Implement API call to update employee data
    // For now, just exit edit mode
    console.log('Save employee data:', editData);
    setIsEditing(false);
    // Later we'll integrate with the API to update the employee data
  };

  // Handle cancel
  const handleCancel = () => {
    setEditData({});
    setIsEditing(false);
  };

  // Handle input change
  const handleInputChange = (field, value) => {
    setEditData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle profile picture upload
  const handleProfilePictureEdit = () => {
    // Placeholder for file upload functionality
    console.log("Edit profile picture");
  };

  // Handle profile picture delete
  const handleProfilePictureDelete = () => {
    if (isEditing) {
      setEditData((prev) => ({ ...prev, ProfilePicture: "" }));
    } else {
      // TODO: Implement API call to delete profile picture
      console.log('Delete profile picture');
    }
  };

  const currentData = isEditing ? editData : employee;

  // Loading state
  if (empLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (empError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <strong>Error:</strong> {empError}
          </div>
          <button 
            onClick={() => user?.empId && fetchEmployeeById(user.empId)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No employee data
  if (!employee) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600 mb-4">No employee data available</p>
          <button 
            onClick={() => user?.empId && fetchEmployeeById(user.empId)}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Load Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="">
        {/* Single Card Layout */}
        <div className="bg-white rounded-xl shadow-lg border border-slate-200 overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-slate-600 to-slate-700 px-6 py-4">
            <div className="flex items-center gap-4">
              {/* Profile Picture with Hover Controls */}
              <div className="relative group">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center text-white text-2xl font-bold overflow-hidden">
                  {currentData.ProfilePicture ? (
                    <img
                      src={currentData.ProfilePicture}
                      alt="Profile"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    `${currentData.FirstName[0]}${currentData.LastName[0]}`
                  )}
                </div>
                {/* Hover Controls */}
                <div className="absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <button
                    onClick={handleProfilePictureEdit}
                    className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                    title="Edit Profile Picture"
                  >
                    <i className="fas fa-edit text-white text-lg"></i>
                  </button>
                  {currentData.ProfilePicture && (
                    <button
                      onClick={handleProfilePictureDelete}
                      className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                      title="Remove Profile Picture"
                    >
                      <i className="fas fa-trash text-white text-lg"></i>
                    </button>
                  )}
                </div>
              </div>

              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div>
                    {isEditing ? (
                      <div className="flex gap-2 mb-1">
                        <input
                          type="text"
                          value={currentData.FirstName}
                          onChange={(e) =>
                            handleInputChange("FirstName", e.target.value)
                          }
                          className="text-lg font-bold bg-white/10 text-white placeholder-white/70 border border-white/20 rounded px-2 py-1 text-lg w-32"
                          placeholder="First Name"
                        />
                        <input
                          type="text"
                          value={currentData.LastName}
                          onChange={(e) =>
                            handleInputChange("LastName", e.target.value)
                          }
                          className="text-lg font-bold bg-white/10 text-white placeholder-white/70 border border-white/20 rounded px-2 py-1 text-lg w-32"
                          placeholder="Last Name"
                        />
                      </div>
                    ) : (
                      <h1 className="text-2xl font-bold text-white mb-1">
                        {currentData.FirstName} {currentData.LastName}
                      </h1>
                    )}
                    <div className="flex items-center gap-4 text-slate-200 text-lg">
                      <span>Employee ID: {currentData.EmpID}</span>         
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${
                          currentData.IsActive
                            ? "bg-green-500 text-white"
                            : "bg-red-500 text-white"
                        }`}
                      >
                        {currentData.IsActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>

                  {/* Edit/Save/Cancel Buttons */}
                  <div className="flex gap-2">
                    {isEditing ? (
                      <>
                        <button
                          onClick={handleSave}
                          className="px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white rounded text-md font-medium transition-colors flex items-center gap-1"
                        >
                          <i className="fas fa-save text-md"></i>
                          Save
                        </button>
                        <button
                          onClick={handleCancel}
                          className="px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded text-md font-medium transition-colors flex items-center gap-1"
                        >
                          <i className="fas fa-times text-md"></i>
                          Cancel
                        </button>
                      </>
                    ) : (
                      <button
                        onClick={handleEdit}
                        className="px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white rounded text-md font-medium transition-colors flex items-center gap-1"
                      >
                        <i className="fas fa-edit text-md"></i>
                        Edit
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content - Two Row Layout */}
          <div className="p-6">
            {/* First Row - Contact and Personal */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Contact Column */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-800 uppercase tracking-wide border-b border-slate-200 pb-2">
                  <i className="fas fa-address-book text-blue-500 mr-2"></i>
                  Contact
                </h3>
                <div className="space-y-3">
                  <div>
                    <div className="text-md text-slate-500 mb-1">
                      Work Email
                    </div>
                    {isEditing ? (
                      <input
                        type="email"
                        value={currentData.WorkEmail}
                        onChange={(e) =>
                          handleInputChange("WorkEmail", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700 break-all">
                        {currentData.WorkEmail}
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="text-md text-slate-500 mb-1">
                      Personal Email
                    </div>
                    {isEditing ? (
                      <input
                        type="email"
                        value={currentData.PersonalEmail}
                        onChange={(e) =>
                          handleInputChange("PersonalEmail", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700 break-all">
                        {currentData.PersonalEmail}
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="text-md text-slate-500 mb-1">Phone</div>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={currentData.PhoneNumber}
                        onChange={(e) =>
                          handleInputChange("PhoneNumber", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {currentData.PhoneNumber}
                      </div>
                    )}
                  </div>

                  <div className="col-span-2">
                    <div className="text-md text-slate-500 mb-1">Address</div>
                    {isEditing ? (
                      <textarea
                        value={currentData.Address}
                        onChange={(e) =>
                          handleInputChange("Address", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                        rows="2"
                      />
                    ) : (
                      <div className="text-lg text-slate-700 leading-relaxed">
                        {currentData.Address}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Personal Column */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-800 uppercase tracking-wide border-b border-slate-200 pb-2">
                  <i className="fas fa-user text-purple-500 mr-2"></i>Personal
                </h3>
                <div className="space-y-3">
                  <div>
                    <div className="text-md text-slate-500 mb-1">Gender</div>
                    {isEditing ? (
                      <select
                        value={currentData.Gender}
                        onChange={(e) =>
                          handleInputChange("Gender", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </select>
                    ) : (
                      <div className="text-lg text-slate-700">
                        {currentData.Gender}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-md text-slate-500 mb-1">
                      Date of Birth
                    </div>
                    {isEditing ? (
                      <input
                        type="date"
                        value={formatDateForInput(currentData.DateOfBirth)}
                        onChange={(e) =>
                          handleInputChange(
                            "DateOfBirth",
                            new Date(e.target.value).toISOString()
                          )
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {formatDate(currentData.DateOfBirth)}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-md text-slate-500 mb-1">Age</div>
                    <div className="text-lg text-slate-700">
                      {calculateAge(currentData.DateOfBirth)} years
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Second Row - Employment and Emergency */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Employment Column */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-800 uppercase tracking-wide border-b border-slate-200 pb-2">
                  <i className="fas fa-briefcase text-green-500 mr-2"></i>
                  Employment
                </h3>
                <div className="space-y-3 grid grid-cols-2 gap-x-4">

                  <div>
                    <div className="text-md text-slate-500 mb-1">Join Date</div>
                    {isEditing ? (
                      <input
                        type="date"
                        value={formatDateForInput(currentData.DateOfJoining)}
                        onChange={(e) =>
                          handleInputChange(
                            "DateOfJoining",
                            new Date(e.target.value).toISOString()
                          )
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {formatDate(currentData.DateOfJoining)}
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="text-md text-slate-500 mb-1">Role ID</div>
                    {isEditing ? (
                      <input
                        type="number"
                        value={currentData.RoleID}
                        onChange={(e) =>
                          handleInputChange("RoleID", parseInt(e.target.value))
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {currentData.RoleID}
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="text-md  text-slate-500 mb-1">
                      Onboarding
                    </div>
                    {isEditing ? (
                      <select
                        value={currentData.OBStatus}
                        onChange={(e) =>
                          handleInputChange(
                            "OBStatus",
                            e.target.value === "true"
                          )
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="false">Pending</option>
                        <option value="true">Completed</option>
                      </select>
                    ) : (
                      <span
                        className={`text-md px-2 py-1 rounded-full font-medium ${
                          currentData.OBStatus
                            ? "bg-green-100 text-green-700"
                            : "bg-yellow-100 text-yellow-700"
                        }`}
                      >
                        {currentData.OBStatus ? "Completed" : "Pending"}
                      </span>
                    )}
                  </div>

                  <div>
                    <div className="text-md text-slate-500 mb-1">Status</div>
              
                      <span
                        className={`text-md px-2 py-1 rounded-full font-medium ${
                          currentData.IsActive
                            ? "bg-green-100 text-green-700"
                            : "bg-red-100 text-red-700"
                        }`}
                      >
                        {currentData.IsActive ? "Active" : "Inactive"}
                      </span>
                  </div>
                </div>
              </div>

              {/* Emergency Column */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-800 uppercase tracking-wide border-b border-slate-200 pb-2">
                  <i className="fas fa-exclamation-triangle text-amber-500 mr-2"></i>
                  Emergency
                </h3>
                <div className="space-y-3">
                  <div>
                    <div className="text-md text-slate-500 mb-1">
                      Contact Name
                    </div>
                    {isEditing ? (
                      <input
                        type="text"
                        value={currentData.EmergencyContact}
                        onChange={(e) =>
                          handleInputChange("EmergencyContact", e.target.value)
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {currentData.EmergencyContact}
                      </div>
                    )}
                  </div>
                  <div>
                    <div className="text-md text-slate-500 mb-1">
                      Contact Number
                    </div>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={currentData.EmergencyContactNumber}
                        onChange={(e) =>
                          handleInputChange(
                            "EmergencyContactNumber",
                            e.target.value
                          )
                        }
                        className="w-full text-md text-slate-700 border border-slate-300 rounded px-2 py-1.5 focus:outline-none focus:ring-1 focus:ring-blue-500"
                      />
                    ) : (
                      <div className="text-lg text-slate-700">
                        {currentData.EmergencyContactNumber}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfilePage;