// Test script to verify weightage calculation
const stepConfig = {
  1: { 
    title: 'Basic Information',
    icon: 'fas fa-user',
    fields: [
      { name: 'firstName', weight: 2 },
      { name: 'lastName', weight: 2 },
      { name: 'email', weight: 2 },
      { name: 'phoneNumber', weight: 2 },
      { name: 'alternatePhoneNumber', weight: 1 },
      { name: 'dateOfBirth', weight: 2 },
      { name: 'gender', weight: 1 },
      { name: 'maritalStatus', weight: 1 },
      { name: 'address', weight: 2 },
      { name: 'city', weight: 1 },
      { name: 'state', weight: 1 },
      { name: 'pincode', weight: 1 },
      { name: 'emergencyContactName', weight: 2 },
      { name: 'emergencyContactPhone', weight: 2 },
      { name: 'emergencyContactRelation', weight: 1 },
    ],
  },
  2: {
    title: 'Posting Details',
    icon: 'fas fa-briefcase',
    fields: [
      { name: 'department', weight: 2 },
      { name: 'position', weight: 2 },
      { name: 'doj', weight: 2 },
      { name: 'obsStatus', weight: 1 },
      { name: 'isActive', weight: 1 },
    ],
  },
  3: {
    title: 'Bank Details',
    icon: 'fas fa-university',
    fields: [
      { name: 'bankName', weight: 2 },
      { name: 'accountNumber', weight: 2 },
      { name: 'ifscCode', weight: 2 },
      { name: 'branchName', weight: 2 },
      { name: 'cancelledCheque', weight: 2 },
    ],
  },
  4: {
    title: 'Academic Details',
    icon: 'fas fa-graduation-cap',
    fields: [
      { name: 'degree', weight: 2.5 },
      { name: 'institution', weight: 2.5 },
      { name: 'yearOfPassing', weight: 2.5 },
      { name: 'grade', weight: 2.5 },
    ],
  },
  5: {
    title: 'Documents Upload',
    icon: 'fas fa-file-upload',
    fields: [
      { name: 'aadharFile', weight: 2 },
      { name: 'aadharDocNumber', weight: 1 },
      { name: 'panFile', weight: 1 },
      { name: 'panDocNumber', weight: 0.5 },
      { name: 'highSchoolFile', weight: 2 },
      { name: 'highSchoolDocNumber', weight: 1 },
      { name: 'intermediateFile', weight: 1 },
      { name: 'intermediateDocNumber', weight: 0.5 },
      { name: 'graduationFile', weight: 1 },
      { name: 'graduationDocNumber', weight: 0.5 },
      { name: 'postGraduationFile', weight: 1 },
      { name: 'postGraduationDocNumber', weight: 0.5 },
    ],
  }
};

// Calculate total weights for each step
let step1Weight = 0, step2Weight = 0, step3Weight = 0, step4Weight = 0, step5Weight = 0;

Object.entries(stepConfig).forEach(([stepNum, step]) => {
  step.fields.forEach((field) => {
    if (stepNum === '1') {
      step1Weight += field.weight;
    } else if (stepNum === '2') {
      step2Weight += field.weight;
    } else if (stepNum === '3') {
      step3Weight += field.weight;
    } else if (stepNum === '4') {
      step4Weight += field.weight;
    } else if (stepNum === '5') {
      step5Weight += field.weight;
    }
  });
});

console.log('Step weights:');
console.log('Step 1:', step1Weight);
console.log('Step 2:', step2Weight);
console.log('Step 3:', step3Weight);
console.log('Step 4:', step4Weight);
console.log('Step 5:', step5Weight);

// Test with all fields completed (100% each step)
const step1Percentage = 100;
const step2Percentage = 100;
const step3Percentage = 100;
const step4Percentage = 100;
const step5Percentage = 100;

// Steps 1 and 2 together make up 50% of total progress
const steps12Combined = (step1Percentage + step2Percentage) / 2; // Average of step 1 and 2
const steps12Contribution = steps12Combined * 0.5; // 50% weight

// Steps 3, 4, and 5 together make up 50% of total progress  
const steps345Combined = (step3Percentage + step4Percentage + step5Percentage) / 3; // Average of step 3, 4, and 5
const steps345Contribution = steps345Combined * 0.5; // 50% weight

const totalPercentage = steps12Contribution + steps345Contribution;

console.log('\nWith all fields completed:');
console.log('Steps 1+2 combined:', steps12Combined);
console.log('Steps 1+2 contribution:', steps12Contribution);
console.log('Steps 3+4+5 combined:', steps345Combined);
console.log('Steps 3+4+5 contribution:', steps345Contribution);
console.log('Total percentage:', Math.round(totalPercentage));
