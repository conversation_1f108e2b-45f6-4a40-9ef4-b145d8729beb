import React, { useState, useEffect, useMemo } from 'react';
import { ChevronDown, ChevronUp, Info } from 'lucide-react';
import { notification, PayrollService } from '@/services';
import getAllDepartments from '@/services/Departments/getAllDepartments';

const CTCAssignment = () => {
  const notify = notification();

  // Form state
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [ctcAmount, setCTCAmount] = useState('0');
  const [ctcPeriod, setCTCPeriod] = useState('monthly'); // 'monthly' or 'yearly'
  const [showAdvanced, setShowAdvanced] = useState(true);
  const [inputMode, setInputMode] = useState('percentage'); // 'percentage' or 'amount'
  const [taxRegime, setTaxRegime] = useState('new'); // 'old' or 'new'
  const [epfApplicable, setEpfApplicable] = useState(true);
  const [professionalTaxApplicable, setProfessionalTaxApplicable] = useState(true);
  const [location, setLocation] = useState('Tamil Nadu');

  // Manual tax and deduction inputs
  const [manualDeductions, setManualDeductions] = useState({
    epfEmployee: '',
    epfEmployer: '',
    professionalTax: '',
    incomeTax: ''
  });

  // Clear manual deductions when period changes
  useEffect(() => {
    setManualDeductions({
      epfEmployee: '',
      epfEmployer: '',
      professionalTax: '',
      incomeTax: ''
    });
  }, [ctcPeriod]);

  // Data states
  const [departments, setDepartments] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);

  // Component configuration
  const [components, setComponents] = useState({
    basicSalary: { percentage: 50, amount: 0, label: 'Basic Salary' },
    hra: { percentage: 30, amount: 0, label: 'HRA' },
    da: { percentage: 20, amount: 0, label: 'DA' },
    lta: { percentage: 0, amount: 0, label: 'LTA' },
    specialAllowance: { percentage: 0, amount: 0, label: 'Special Allowance' },
    performanceBonus: { percentage: 0, amount: 0, label: 'Performance Bonus' }
  });

  // Indian states for location dropdown
  const indianStates = [
    'Andhra Pradesh', 'Assam', 'Bihar', 'Chandigarh', 'Chhattisgarh', 'Delhi', 
    'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 
    'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 
    'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Tamil Nadu', 'Telangana', 
    'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal'
  ];

  // Load departments on component mount
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const response = await getAllDepartments();
        setDepartments(response);
      } catch (error) {
        notify.error('Failed to load departments');
      }
    };
    fetchDepartments();
  }, []);

  // Load employees when department changes
  useEffect(() => {
    if (selectedDepartment) {
      const fetchEmployees = async () => {
        try {
          setLoading(true);
          const response = await PayrollService.getEmployeesByDepartment(selectedDepartment);
          setEmployees(response);
        } catch (error) {
          notify.error('Failed to load employees');
        } finally {
          setLoading(false);
        }
      };
      fetchEmployees();
    } else {
      setEmployees([]);
      setSelectedEmployee('');
    }
  }, [selectedDepartment]);

  // Calculate CTC breakdown
  const calculations = useMemo(() => {
    const yearlyCtc = ctcPeriod === 'yearly' ? parseFloat(ctcAmount) *12 || 0 : (parseFloat(ctcAmount) || 0) * 12;
    const monthlyCtc = yearlyCtc / 12;
    const currentPeriodCtc = ctcPeriod === 'yearly' ? yearlyCtc : monthlyCtc;

    // Calculate component amounts based on current period
    const earnings = {};
    let totalPercentage = 0;

    Object.entries(components).forEach(([key, comp]) => {
      if (inputMode === 'percentage') {
        earnings[key] = (currentPeriodCtc * comp.percentage) / 100;
        totalPercentage += comp.percentage;
      } else {
        // Component amounts are entered based on current period
        earnings[key] = comp.amount;
      }
    });

    // If using percentage mode and total doesn't equal 100%, adjust basic salary
    if (inputMode === 'percentage' && totalPercentage !== 100) {
      const remaining = 100 - totalPercentage + components.basicSalary.percentage;
      earnings.basicSalary = (currentPeriodCtc * remaining) / 100;
    }

    const grossSalary = Object.values(earnings).reduce((sum, amount) => sum + amount, 0);
    const yearlyGrossSalary = ctcPeriod === 'yearly' ? grossSalary : grossSalary * 12;
    const monthlyGrossSalary = ctcPeriod === 'monthly' ? grossSalary : grossSalary / 12;

    // Tax calculations (always calculated annually, then converted to period)
    const standardDeduction = taxRegime === 'new' ? 75000 : 50000;
    const yearlyTaxableIncome = Math.max(0, yearlyGrossSalary - standardDeduction);

    // EPF calculations (if applicable) - use manual input or calculate
    const epfLimit = ctcPeriod === 'yearly' ? 21600 : 1800; // Annual limit: 21,600, Monthly limit: 1,800
    const calculatedEpfEmployee = epfApplicable ? Math.min(earnings.basicSalary * 0.12, epfLimit) : 0;
    const epfEmployee = manualDeductions.epfEmployee !== '' ? parseFloat(manualDeductions.epfEmployee) || 0 : calculatedEpfEmployee;
    
    const calculatedEpfEmployer = epfApplicable ? calculatedEpfEmployee : 0;
    const epfEmployer = manualDeductions.epfEmployer !== '' ? parseFloat(manualDeductions.epfEmployer) || 0 : calculatedEpfEmployer;
    
    // ESI calculations (if gross < 25000/month)
    const esiApplicable = monthlyGrossSalary <= 25000;
    const esiEmployee = esiApplicable ? grossSalary * 0.0075 : 0; // 0.75%
    const esiEmployer = esiApplicable ? grossSalary * 0.0325 : 0; // 3.25%

    // Professional Tax - use manual input or calculate (varies by period)
    const ptThreshold = ctcPeriod === 'yearly' ? 180000 : 15000; // Annual: 1.8L, Monthly: 15K
    const ptAmount = ctcPeriod === 'yearly' ? 2400 : 200; // Annual: 2400, Monthly: 200
    const calculatedProfessionalTax = professionalTaxApplicable ? (grossSalary > ptThreshold ? ptAmount : 0) : 0;
    const professionalTax = manualDeductions.professionalTax !== '' ? parseFloat(manualDeductions.professionalTax) || 0 : calculatedProfessionalTax;

    // Income Tax calculation (simplified) - always calculate annually first
    let yearlyCalculatedIncomeTax = 0;
    if (taxRegime === 'new') {
      // New tax regime rates (2025-2026)
      if (yearlyTaxableIncome > 300000) {
        if (yearlyTaxableIncome <= 600000) {
          yearlyCalculatedIncomeTax = (yearlyTaxableIncome - 300000) * 0.05;
        } else if (yearlyTaxableIncome <= 900000) {
          yearlyCalculatedIncomeTax = 15000 + (yearlyTaxableIncome - 600000) * 0.10;
        } else if (yearlyTaxableIncome <= 1200000) {
          yearlyCalculatedIncomeTax = 45000 + (yearlyTaxableIncome - 900000) * 0.15;
        } else if (yearlyTaxableIncome <= 1500000) {
          yearlyCalculatedIncomeTax = 90000 + (yearlyTaxableIncome - 1200000) * 0.20;
        } else {
          yearlyCalculatedIncomeTax = 150000 + (yearlyTaxableIncome - 1500000) * 0.30;
        }
      }
    } else {
      // Old tax regime calculation (simplified)
      if (yearlyTaxableIncome > 250000) {
        if (yearlyTaxableIncome <= 500000) {
          yearlyCalculatedIncomeTax = (yearlyTaxableIncome - 250000) * 0.05;
        } else if (yearlyTaxableIncome <= 1000000) {
          yearlyCalculatedIncomeTax = 12500 + (yearlyTaxableIncome - 500000) * 0.20;
        } else {
          yearlyCalculatedIncomeTax = 112500 + (yearlyTaxableIncome - 1000000) * 0.30;
        }
      }
    }

    // Health and Education Cess (4% of income tax)
    const yearlyHealthEducationCess = yearlyCalculatedIncomeTax * 0.04;
    const yearlyTotalIncomeTax = yearlyCalculatedIncomeTax + yearlyHealthEducationCess;
    
    // Convert to current period
    const calculatedTotalIncomeTax = ctcPeriod === 'yearly' ? yearlyTotalIncomeTax : yearlyTotalIncomeTax / 12;
    
    // Use manual input or calculated value
    const totalIncomeTax = manualDeductions.incomeTax !== '' ? parseFloat(manualDeductions.incomeTax) || 0 : calculatedTotalIncomeTax;

    // Total deductions
    const totalDeductions = epfEmployee + esiEmployee + professionalTax + totalIncomeTax;

    // Net salary (current period)
    const netSalary = grossSalary - totalDeductions;
    const netAnnualSalary = ctcPeriod === 'yearly' ? netSalary : netSalary * 12;
    const netMonthlySalary = ctcPeriod === 'monthly' ? netSalary : netSalary / 12;

    // Cost to company breakdown
    const totalEmployerContribution = epfEmployer + esiEmployer;

    // Convert values for display based on period
    const displayTaxableIncome = ctcPeriod === 'yearly' ? yearlyTaxableIncome : yearlyTaxableIncome / 12;
    const displayStandardDeduction = ctcPeriod === 'yearly' ? standardDeduction : standardDeduction / 12;

    return {
      yearlyCtc,
      monthlyCtc,
      currentPeriodCtc,
      earnings,
      grossSalary,
      standardDeduction: displayStandardDeduction,
      taxableIncome: displayTaxableIncome,
      deductions: {
        epfEmployee,
        epfEmployer,
        esiEmployee,
        esiEmployer,
        professionalTax,
        incomeTax: totalIncomeTax,
        totalDeductions
      },
      calculatedValues: {
        epfEmployee: calculatedEpfEmployee,
        epfEmployer: calculatedEpfEmployer,
        professionalTax: calculatedProfessionalTax,
        incomeTax: calculatedTotalIncomeTax
      },
      netSalary,
      netAnnualSalary,
      netMonthlySalary,
      totalEmployerContribution,
      finalCtc: grossSalary + totalEmployerContribution
    };
  }, [ctcAmount, ctcPeriod, components, inputMode, taxRegime, epfApplicable, professionalTaxApplicable, location, manualDeductions]);

  // Handle component change
  const handleComponentChange = (componentKey, field, value) => {
    setComponents(prev => ({
      ...prev,
      [componentKey]: {
        ...prev[componentKey],
        [field]: parseFloat(value) || 0
      }
    }));
  };

  // Handle manual deduction change
  const handleManualDeductionChange = (field, value) => {
    setManualDeductions(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save CTC assignment
  const handleSave = async () => {
    if (!selectedDepartment || !selectedEmployee || !ctcAmount) {
      notify.error('Please select department, employee and enter CTC amount');
      return;
    }

    try {
      setLoading(true);
      
      const ctcData = {
        employeeId: selectedEmployee,
        departmentId: selectedDepartment,
        ctcAmount: calculations.yearlyCtc,
        ctcBreakdown: {
          ...calculations.earnings,
          grossSalary: calculations.grossSalary,
          netSalary: calculations.netAnnualSalary,
          totalDeductions: calculations.deductions.totalDeductions
        },
        taxRegime,
        epfApplicable,
        professionalTaxApplicable,
        location,
        effectiveDate: new Date().toISOString()
      };

      // This would call an API to save the CTC assignment
      // await PayrollService.assignEmployeeCTC(ctcData);
      
      notify.success('CTC assigned successfully!');
      
    } catch (error) {
      notify.error('Failed to assign CTC: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-800 mb-8">CTC Assignment</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Panel - CTC Configuration */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Cost to Company (CTC)</h2>
          
          {/* Department and Employee Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Department</option>
                {departments.map((dept) => (
                  <option key={dept.DeptID} value={dept.DeptID}>
                    {dept.DeptName}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Employee</label>
              <select
                value={selectedEmployee}
                onChange={(e) => setSelectedEmployee(e.target.value)}
                disabled={!selectedDepartment || loading}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select Employee</option>
                {employees.map((emp) => (
                  <option key={emp.EmpID} value={emp.EmpID}>
                    {emp.FirstName} {emp.LastName} ({emp.EmpID})
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* CTC Input */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl font-bold">₹</span>
              <input
                type="number"
                value={ctcAmount}
                onChange={(e) => setCTCAmount(e.target.value)}
                className="text-2xl font-bold w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="7,80,000"
              />
              <span className="text-sm text-gray-500">{ctcPeriod}</span>
            </div>
            <p className="text-sm text-gray-500 mb-4">
              Enter your {ctcPeriod} Cost to Company (CTC) amount in Indian Rupees. All calculations will be shown in {ctcPeriod} format.
            </p>
            
            {/* Monthly/Yearly Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setCTCPeriod('monthly')}
                className={`flex-1 py-2 px-4 rounded-md font-medium ${
                  ctcPeriod === 'monthly' 
                    ? 'bg-white text-blue-600 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setCTCPeriod('yearly')}
                className={`flex-1 py-2 px-4 rounded-md font-medium ${
                  ctcPeriod === 'yearly' 
                    ? 'bg-blue-600 text-white shadow-sm' 
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Yearly
              </button>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="border-t pt-6">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center gap-2 text-blue-600 hover:text-blue-800 mb-4"
            >
              Advanced Settings
              {showAdvanced ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>

            {showAdvanced && (
              <div className="space-y-6">
                {/* Customize Components */}
                <div>
                  <h3 className="font-semibold text-gray-800 mb-4">Customize Components</h3>
                  
                  {/* Input Mode Toggle */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Input Mode:</label>
                    <div className="flex gap-4">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="percentage"
                          checked={inputMode === 'percentage'}
                          onChange={(e) => setInputMode(e.target.value)}
                          className="mr-2"
                        />
                        Percentage
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          value="amount"
                          checked={inputMode === 'amount'}
                          onChange={(e) => setInputMode(e.target.value)}
                          className="mr-2"
                        />
                        Amount ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})
                      </label>
                    </div>
                  </div>

                  {/* Component Configuration */}
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm font-medium text-gray-600">
                      <span>Component</span>
                      <span>{inputMode === 'percentage' ? 'Percentage' : `Amount (${ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})`}</span>
                    </div>
                    
                    {Object.entries(components).map(([key, component]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{component.label}</span>
                          <Info size={14} className="text-gray-400" />
                        </div>
                        <input
                          type="number"
                          value={inputMode === 'percentage' ? component.percentage : component.amount}
                          onChange={(e) => handleComponentChange(
                            key, 
                            inputMode === 'percentage' ? 'percentage' : 'amount', 
                            e.target.value
                          )}
                          className="w-20 p-2 text-right border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                          max={inputMode === 'percentage' ? '100' : undefined}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tax Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Tax Regime:</label>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setTaxRegime('old')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          taxRegime === 'old'
                            ? 'bg-gray-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        Old
                      </button>
                      <button
                        onClick={() => setTaxRegime('new')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          taxRegime === 'new'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        New
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location:</label>
                    <select
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {indianStates.map((state) => (
                        <option key={state} value={state}>
                          {state}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Tax Applicability */}
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={epfApplicable}
                      onChange={(e) => setEpfApplicable(e.target.checked)}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">EPF Applicable</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={professionalTaxApplicable}
                      onChange={(e) => setProfessionalTaxApplicable(e.target.checked)}
                      className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm">Professional Tax Applicable</span>
                  </label>
                </div>

                {/* Manual Deduction Inputs */}
                <div>
                  <h3 className="font-semibold text-gray-800 mb-4">Manual Deduction Override</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Leave fields empty to use calculated values. Enter custom {ctcPeriod === 'yearly' ? 'annual' : 'monthly'} amounts to override calculations.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* EPF Employee */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EPF Employee ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={manualDeductions.epfEmployee}
                          onChange={(e) => handleManualDeductionChange('epfEmployee', e.target.value)}
                          placeholder={`Auto: ₹${calculations.calculatedValues.epfEmployee.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                        />
                        <span className="absolute right-3 top-2 text-gray-400 text-sm">₹</span>
                      </div>
                    </div>

                    {/* EPF Employer */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        EPF Employer ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={manualDeductions.epfEmployer}
                          onChange={(e) => handleManualDeductionChange('epfEmployer', e.target.value)}
                          placeholder={`Auto: ₹${calculations.calculatedValues.epfEmployer.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                        />
                        <span className="absolute right-3 top-2 text-gray-400 text-sm">₹</span>
                      </div>
                    </div>

                    {/* Professional Tax */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Professional Tax ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={manualDeductions.professionalTax}
                          onChange={(e) => handleManualDeductionChange('professionalTax', e.target.value)}
                          placeholder={`Auto: ₹${calculations.calculatedValues.professionalTax.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                        />
                        <span className="absolute right-3 top-2 text-gray-400 text-sm">₹</span>
                      </div>
                    </div>

                    {/* Income Tax */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Income Tax ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={manualDeductions.incomeTax}
                          onChange={(e) => handleManualDeductionChange('incomeTax', e.target.value)}
                          placeholder={`Auto: ₹${calculations.calculatedValues.incomeTax.toLocaleString('en-IN', { maximumFractionDigits: 0 })}`}
                          className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          min="0"
                        />
                        <span className="absolute right-3 top-2 text-gray-400 text-sm">₹</span>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="mt-4 flex gap-2">
                    <button
                      onClick={() => setManualDeductions({
                        epfEmployee: calculations.calculatedValues.epfEmployee.toString(),
                        epfEmployer: calculations.calculatedValues.epfEmployer.toString(),
                        professionalTax: calculations.calculatedValues.professionalTax.toString(),
                        incomeTax: calculations.calculatedValues.incomeTax.toString()
                      })}
                      className="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                    >
                      Use Calculated Values
                    </button>
                    <button
                      onClick={() => setManualDeductions({
                        epfEmployee: '',
                        epfEmployer: '',
                        professionalTax: '',
                        incomeTax: ''
                      })}
                      className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <div className="mt-8">
            <button
              onClick={handleSave}
              disabled={loading || !selectedDepartment || !selectedEmployee || !ctcAmount}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : 'Assign CTC'}
            </button>
          </div>
        </div>

        {/* Right Panel - Salary Breakdown */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex justify-between items-start mb-6">
            <h2 className="text-xl font-semibold">
              Salary Breakdown under {taxRegime === 'new' ? 'New' : 'Old'} Tax Regime (2025-2026)
            </h2>
            <div className="text-right">
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <span className="bg-blue-100 text-blue-600 px-1 rounded">M</span>
                <span>= Manual Override</span>
              </div>
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">
                Net {ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'} Salary
              </div>
              <div className="text-2xl font-bold text-green-600">
                ₹{calculations.netSalary.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">
                Net {ctcPeriod === 'yearly' ? 'Monthly Equivalent' : 'Annual Equivalent'}
              </div>
              <div className="text-2xl font-bold text-blue-600">
                ₹{ctcPeriod === 'yearly' ? calculations.netMonthlySalary.toLocaleString('en-IN', { maximumFractionDigits: 0 }) : calculations.netAnnualSalary.toLocaleString('en-IN', { maximumFractionDigits: 0 })}
              </div>
            </div>
          </div>

          {/* Earnings Breakdown */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-800 mb-3">Earnings</h3>
            <div className="space-y-2">
              {Object.entries(calculations.earnings).map(([key, amount]) => {
                const component = components[key];
                if (amount === 0) return null;
                return (
                  <div key={key} className="flex justify-between text-sm">
                    <span>{component.label}</span>
                    <span>₹{amount.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Gross Salary */}
          <div className="border-t pt-4 mb-6">
            <div className="flex justify-between font-semibold">
              <span>Gross Salary</span>
              <span>₹{calculations.grossSalary.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
            </div>
          </div>

          {/* Tax Information */}
          <div className="mb-6">
            <div className="flex justify-between text-sm text-red-600">
              <span>Standard Deduction ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'})</span>
              <span>-₹{calculations.standardDeduction.toLocaleString('en-IN')}</span>
            </div>
            <div className="flex justify-between text-sm mt-2">
              <span>Taxable Income ({ctcPeriod === 'yearly' ? 'Annual' : 'Monthly'}) (before other deductions)</span>
              <span>₹{calculations.taxableIncome.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
            </div>
          </div>

          {/* Deductions */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-800 mb-3">Deductions</h3>
            <div className="space-y-2 text-sm">
              {calculations.deductions.epfEmployee > 0 && (
                <div className="flex justify-between text-red-600">
                  <div className="flex items-center gap-1">
                    <span>EPF (Employee)</span>
                    {manualDeductions.epfEmployee !== '' && (
                      <span className="text-xs bg-blue-100 text-blue-600 px-1 rounded" title="Manual override">M</span>
                    )}
                  </div>
                  <span>-₹{calculations.deductions.epfEmployee.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
              )}
              {calculations.deductions.epfEmployee > 0 && (
                <div className="flex justify-between text-gray-600">
                  <div className="flex items-center gap-1">
                    <span>EPF (Employer)</span>
                    {manualDeductions.epfEmployer !== '' && (
                      <span className="text-xs bg-blue-100 text-blue-600 px-1 rounded" title="Manual override">M</span>
                    )}
                  </div>
                  <span>₹{calculations.deductions.epfEmployer.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
              )}
              {calculations.deductions.esiEmployee > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>ESI</span>
                  <span>-₹{calculations.deductions.esiEmployee.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
              )}
              {calculations.deductions.professionalTax > 0 && (
                <div className="flex justify-between text-red-600">
                  <div className="flex items-center gap-1">
                    <span>Professional Tax</span>
                    {manualDeductions.professionalTax !== '' && (
                      <span className="text-xs bg-blue-100 text-blue-600 px-1 rounded" title="Manual override">M</span>
                    )}
                  </div>
                  <span>-₹{calculations.deductions.professionalTax.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
              )}
              {calculations.deductions.incomeTax > 0 && (
                <div className="flex justify-between text-red-600">
                  <div className="flex items-center gap-1">
                    <span>Income Tax</span>
                    {manualDeductions.incomeTax !== '' && (
                      <span className="text-xs bg-blue-100 text-blue-600 px-1 rounded" title="Manual override">M</span>
                    )}
                  </div>
                  <span>-₹{calculations.deductions.incomeTax.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
              )}
            </div>
          </div>

          {/* Summary */}
          <div className="border-t pt-4">
            <h3 className="font-semibold text-gray-800 mb-3">Summary</h3>
            <div className="flex justify-between font-semibold text-red-600 mb-4">
              <span>Total Deductions</span>
              <span>-₹{calculations.deductions.totalDeductions.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="font-semibold text-gray-800 mb-3">Cost to Company Breakup</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Gross Salary</span>
                  <span>₹{calculations.grossSalary.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                </div>
                {calculations.deductions.epfEmployer > 0 && (
                  <div className="flex justify-between">
                    <div className="flex items-center gap-1">
                      <span>Employer EPF Contribution</span>
                      {manualDeductions.epfEmployer !== '' && (
                        <span className="text-xs bg-blue-100 text-blue-600 px-1 rounded" title="Manual override">M</span>
                      )}
                    </div>
                    <span>₹{calculations.deductions.epfEmployer.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                  </div>
                )}
                {calculations.deductions.esiEmployer > 0 && (
                  <div className="flex justify-between">
                    <span>Employer ESI Contribution</span>
                    <span>₹{calculations.deductions.esiEmployer.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                  </div>
                )}
                <div className="border-t pt-2 font-semibold">
                  <div className="flex justify-between">
                    <span>Total CTC</span>
                    <span>₹{calculations.finalCtc.toLocaleString('en-IN', { maximumFractionDigits: 0 })}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CTCAssignment;
