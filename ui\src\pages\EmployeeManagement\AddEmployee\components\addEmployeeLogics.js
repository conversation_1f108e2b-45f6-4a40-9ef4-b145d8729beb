// addEmployeeLogic.js
import notification from '@/services/NotificationService';

const notify = notification();
// Step configuration

export const stepConfig = {
  1: { 
    title: 'Basic Information',
    icon: 'fas fa-user',
    fields: [
      // Total Weightage: 
      { name: 'firstName', weight: 2 },
      { name: 'lastName', weight: 2 },
      { name: 'email', weight: 2 },
      { name: 'phoneNumber', weight: 2 },
      { name: 'alternatePhoneNumber', weight: 2 },
      { name: 'dateOfBirth', weight: 2 },
      { name: 'gender', weight: 1 },
      { name: 'maritalStatus', weight: 1 },
      { name: 'address', weight: 2 },
      { name: 'city', weight: 1 },
      { name: 'state', weight: 1 },
      { name: 'pincode', weight: 1 },
      { name: 'emergencyContactName', weight: 1 },
      { name: 'emergencyContactPhone', weight: 1 },
      { name: 'emergencyContactRelation', weight: 1 },
    ],
  },
  2: {
    // Total Weightage: 
    title: 'Posting Details',
    icon: 'fas fa-briefcase',
    fields: [
      { name: 'department', weight: 1 },
      { name: 'position', weight: 1 },
      { name: 'doj', weight: 1 },
      { name: 'obsStatus', weight: 1 },
      { name: 'isActive', weight: 1 },
    ],
  },
  3: {
    // Total Weightage: 
    title: 'Bank Details',
    icon: 'fas fa-university',
    fields: [
      { name: 'bankName', weight: 1 },
      { name: 'accountNumber', weight: 1 },
      { name: 'ifscCode', weight: 2 },
      { name: 'branchName', weight: 2 },
      { name: 'cancelledCheque', weight: 2 },
    ],
  },
  4: {
    // Total Weightage: 
    title: 'Academic Details',
    icon: 'fas fa-graduation-cap',
    fields: [
      { name: 'degree', weight: 2 },
      { name: 'institution', weight: 2 },
      { name: 'yearOfPassing', weight: 2 },
      { name: 'grade', weight: 2 },
    ],
  },
  5: {
    title: 'Documents Upload',
    icon: 'fas fa-file-upload',
    fields: [
      // Total Weightage: 
      { name: 'aadharFile', weight: 2 },
      { name: 'aadharDocNumber', weight: 1 },
      { name: 'panFile', weight: 1 },
      { name: 'panDocNumber', weight: 1 },
      { name: 'highSchoolFile', weight: 2 },
      { name: 'highSchoolDocNumber', weight: 1 },
      { name: 'intermediateFile', weight: 1 },
      { name: 'intermediateDocNumber', weight: 1 },
      { name: 'graduationFile', weight: 1 },
      { name: 'graduationDocNumber', weight: 1 },
      { name: 'postGraduationFile', weight: 1 },
      { name: 'postGraduationDocNumber', weight: 1 },
    ],
  }
};

// Initial form data
export const initialFormData = {
  firstName: '',
  // middleName: '',
  lastName: '',
  personalEmail: '',
  workEmail: '',
  phoneNumber: '',
  address: '',
  emergencyContact: '',
  emergencyContactNumber: '',
  dob: '',
  gender: '',
  doj: '',
  obsStatus: 'pending', // Default value
  isActive: false, // Default value
  profilePicture: null,
  bankName: '',
  accountNumber: '',
  ifscCode: '',
  branchName: '',
  cancelledCheque: null,
  degree: '',
  institution: '',
  yearOfPassing: '',
  grade: '',
  department: '',
  position: '',
};

// Calculate completion percentage
export const calculateCompletionDebug = (formData) => {
  console.log('=== Completion Calculation Debug ===');
  
  const getStepCompletion = (stepNum) => {
    const step = stepConfig[stepNum];
    if (!step) return { completion: 0, details: 'Step not found' };

    let totalWeight = 0;
    let completedWeight = 0;
    const fieldDetails = [];

    step.fields.forEach((field) => {
      totalWeight += field.weight;
      
      const fieldValue = formData[field.name];
      let isCompleted = false;

      if (fieldValue !== null && fieldValue !== undefined) {
        if (typeof fieldValue === 'string') {
          isCompleted = fieldValue.trim() !== '';
        } else if (fieldValue instanceof File) {
          isCompleted = true;
        } else if (typeof fieldValue === 'boolean') {
          isCompleted = true;
        } else if (typeof fieldValue === 'number') {
          isCompleted = true;
        } else {
          isCompleted = true;
        }
      }

      if (isCompleted) {
        completedWeight += field.weight;
      }

      fieldDetails.push({
        field: field.name,
        weight: field.weight,
        completed: isCompleted,
        value: fieldValue
      });
    });

    const completion = totalWeight > 0 ? (completedWeight / totalWeight) : 0;
    
    return {
      completion,
      totalWeight,
      completedWeight,
      fieldDetails
    };
  };

  // Calculate each step
  const step1 = getStepCompletion(1);
  const step2 = getStepCompletion(2);
  const step3 = getStepCompletion(3);
  const step4 = getStepCompletion(4);
  const step5 = getStepCompletion(5);

  console.log('Step 1:', step1);
  console.log('Step 2:', step2);
  console.log('Step 3:', step3);
  console.log('Step 4:', step4);
  console.log('Step 5:', step5);

  // Steps 1 & 2 contribute 50% total (25% each)
  const step1Contribution = step1.completion * 25;
  const step2Contribution = step2.completion * 25;
  const steps12Total = step1Contribution + step2Contribution;

  // Steps 3, 4 & 5 contribute remaining 50% total
  const step3Contribution = step3.completion * 16.67;
  const step4Contribution = step4.completion * 16.67;
  const step5Contribution = step5.completion * 16.66;
  const steps345Total = step3Contribution + step4Contribution + step5Contribution;

  const totalPercentage = steps12Total + steps345Total;

  console.log('Step 1 Contribution (25%):', step1Contribution);
  console.log('Step 2 Contribution (25%):', step2Contribution);
  console.log('Steps 1+2 Total (50%):', steps12Total);
  console.log('Step 3 Contribution (16.67%):', step3Contribution);
  console.log('Step 4 Contribution (16.67%):', step4Contribution);
  console.log('Step 5 Contribution (16.66%):', step5Contribution);
  console.log('Steps 3+4+5 Total (50%):', steps345Total);
  console.log('Final Total:', totalPercentage);
  console.log('=== End Debug ===');

  return Math.round(totalPercentage);
};

// Handle input changes
export const handleInputChange = (e, setFormData, setErrors, errors) => {
  const { name, value, type, checked, files } = e.target;
  setFormData((prev) => ({
    ...prev,
    [name]: type === 'checkbox' ? checked : type === 'file' ? files[0] : value,
  }));

  if (errors[name]) {
    setErrors((prev) => ({
      ...prev,
      [name]: '',
    }));
  }
};



// Validate current step
export const validateCurrentStep = (currentStep, formData, setErrors) => {
  const newErrors = {};

  // Validate required fields based on current step
  if (currentStep === 1) {
    // Required fields validation
    if (!formData.firstName?.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.phoneNumber?.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^\d{10}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Phone number must be exactly 10 digits';
    }

    if (!formData.dob?.trim()) {
      newErrors.dob = 'Date of birth is required';
    }

    if (!formData.gender?.trim()) {
      newErrors.gender = 'Gender is required';
    }

    if (!formData.address?.trim()) {
      newErrors.address = 'Address is required';
    }

    // EmergencyContactNumber optional but validate format if provided
    if (formData.emergencyContactNumber && formData.emergencyContactNumber.trim() !== '') {
      if (!/^\d{10}$/.test(formData.emergencyContactNumber)) {
        newErrors.emergencyContactNumber = 'Emergency contact number must be exactly 10 digits';
      }
    }
  }
  else if (currentStep === 2) {
    // Posting Details validations
    if (!formData.department?.trim()) {
      newErrors.department = 'Department is required';
    }
    if (!formData.position?.trim()) {
      newErrors.position = 'Position is required';
    }
  }
   else if (currentStep === 3) {
    // Bank Details validations
    if (!formData.bankName?.trim()) {
      newErrors.bankName = 'Bank name is required';
    }
    if (!formData.accountNumber?.trim()) {
      newErrors.accountNumber = 'Account number is required';
    }
    if (!formData.ifscCode?.trim()) {
      newErrors.ifscCode = 'IFSC code is required';
    }
    if (!formData.branchName?.trim()) {
      newErrors.branchName = 'Branch name is required';
    }
    if (!formData.cancelledCheque || !(formData.cancelledCheque instanceof File)) {
      newErrors.cancelledCheque = 'Cancelled cheque file is required';
    }
  } 
  else if (currentStep === 4) {
    // Academic Details validations
    if (!formData.degree?.trim()) {
      newErrors.degree = 'Degree is required';
    }
    if (!formData.institution?.trim()) {
      newErrors.institution = 'Institution is required';
    }
    if (!formData.yearOfPassing?.trim()) {
      newErrors.yearOfPassing = 'Year of passing is required';
    }
    if (!formData.grade?.trim()) {
      newErrors.grade = 'Grade is required';
    }
  }
  else if (currentStep === 5) {
    // Documents validations - only required documents
    if (!formData.aadharDocNumber?.trim()) {
      newErrors.aadharDocNumber = 'Aadhar number is required';
    }
    if (!formData.aadharFile || !(formData.aadharFile instanceof File)) {
      newErrors.aadharFile = 'Aadhar document is required';
    }
    if (!formData.highSchoolDocNumber?.trim()) {
      newErrors.highSchoolDocNumber = 'High School certificate number is required';
    }
    if (!formData.highSchoolFile || !(formData.highSchoolFile instanceof File)) {
      newErrors.highSchoolFile = 'High School certificate is required';
    }
  } 
  

  setErrors(newErrors);
  const isValid = Object.keys(newErrors).length === 0;

  if (!isValid) {
    notify.error('Please fill in all required fields!');
  }

  return isValid;
};

// Handle next step
export const nextStep = (currentStep, formData, setErrors, setCurrentStep) => {
  if (validateCurrentStep(currentStep, formData, setErrors)) {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
    notify.success(`Step ${currentStep} completed successfully!`);
  }
};

// Handle previous step
export const prevStep = (setCurrentStep) => {
  setCurrentStep((prev) => Math.max(prev - 1, 1));
};

// Handle form submission
export const handleSubmit = async (e, currentStep, formData, setErrors, setCurrentStep, setIsSubmitting, setFormData) => {
  e.preventDefault();

  if (!validateCurrentStep(currentStep, formData, setErrors)) return;

  // If not on final step, just go to the next step
  if (currentStep < 5) {
    nextStep(currentStep, formData, setErrors, setCurrentStep);
    return;
  }

  // Final step submission
  setIsSubmitting(true);
  notify.loading('Submitting employee information...');

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    notify.success('Employee registered successfully!');

    // Optional: Reset form or redirect
    setFormData(initialFormData);
    setCurrentStep(1);

  } catch (error) {
    notify.error('Failed to submit employee information. Please try again.');
  } finally {
    setIsSubmitting(false);
  }
};
