﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EMS.Models
{
    public class Employee
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int EmpID { get; set; }

        [Required]
        public string FirstName { get; set; }

        public string? LastName { get; set; }

        [Required]
        [EmailAddress]
        public string PersonalEmail { get; set; }

        [EmailAddress]
        public string? WorkEmail { get; set; }

        [Phone]
        [MaxLength(10)]
        public string? PhoneNumber { get; set; }

        public string? Address { get; set; }

        public string? EmergencyContact { get; set; }

        public string? EmergencyContactNumber { get; set; }

        public DateTime? DateOfBirth { get; set; }
        
        public string? Gender { get; set; }

        public DateTime? DateOfJoining { get; set; }

        public bool OBStatus { get; set; }

        public bool IsActive { get; set; }

        public string? ProfilePicture { get; set; }

        public string? Signature { get; set; }

        public int RoleID { get; set; }

    }
}
