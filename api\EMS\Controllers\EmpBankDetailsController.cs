﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;
using EMS.Models.NonDBModels;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpBankDetailsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpBankDetailsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpBankDetails
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpBankDetail>>> GetEmpBankDetails()
        {
            return await _context.EmpBankDetails.ToListAsync();
        }

        // GET: api/EmpBankDetails/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpBankDetail>> GetEmpBankDetail(int id)
        {
            var empBankDetail = await _context.EmpBankDetails.FirstOrDefaultAsync(edp => edp.EmpID == id);

            if (empBankDetail == null)
            {
                return NotFound();
            }

            return empBankDetail;
        }

        // PUT: api/EmpBankDetails/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpBankDetail(int id, EmpBankDetail empBankDetail)
        {
            if (id != empBankDetail.EbdID)
            {
                return BadRequest();
            }

            _context.Entry(empBankDetail).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpBankDetailExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpBankDetails
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpBankDetail>> PostEmpBankDetail(EmpBankDetailsDTO empBankDetail)
        {

            if (empBankDetail == null)
            {
                return BadRequest("EmpBankDetail data is null.");
            }
            var uploadsFolderPath1 = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "CancelledCheque");
            if (!Directory.Exists(uploadsFolderPath1))
            {
                Directory.CreateDirectory(uploadsFolderPath1);
            }

            var emp = await _context.EmpCreds.FindAsync(empBankDetail.EmpID);
            var filename = $"{emp.UserName}_CancelledCheque.{empBankDetail.CancelledCheque.FileName.Split('.')[1]}";

            var filePath = Path.Combine(uploadsFolderPath1, filename);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await empBankDetail.CancelledCheque.CopyToAsync(stream);
            }
            var empBankDetail1 = new EmpBankDetail
            {
                EmpID = empBankDetail.EmpID,
                BankName = empBankDetail.BankName,
                AccountNumber = empBankDetail.AccountNumber,
                IFSCCode = empBankDetail.IFSCCode,
                BranchName = empBankDetail.BranchName,
                CancelledCheque = $"/CancelledCheque/{filename}" // Store the file name instead of the file path
            };
            _context.EmpBankDetails.Add(empBankDetail1);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpBankDetail", new { id = empBankDetail1.EbdID }, empBankDetail1);
        }

        // DELETE: api/EmpBankDetails/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpBankDetail(int id)
        {
            var empBankDetail = await _context.EmpBankDetails.FindAsync(id);
            if (empBankDetail == null)
            {
                return NotFound();
            }

            _context.EmpBankDetails.Remove(empBankDetail);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpBankDetailExists(int id)
        {
            return _context.EmpBankDetails.Any(e => e.EbdID == id);
        }
    }
}
