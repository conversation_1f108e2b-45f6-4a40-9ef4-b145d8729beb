﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System;

namespace EMS.Models
{
    public class EmpPayroll
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int EmpPayrollID { get; set; }

        public int EmpID { get; set; }

        public double? BasicSalary { get; set; }

        public double? HRA { get; set; }

        public double? DA { get; set; }

        public double? HourlyRate { get; set; }

        public double? OtherAllowances { get; set; }

        public double? GrossSalary { get; set; }

        public double? ProvidentFund { get; set; }

        public double? ProfessionalTax { get; set; }

        public double? IncomeTax { get; set; }

        public double? OtherDeductions { get; set; }

        public double? TotalDeductions { get; set; }

        public double? NetSalary { get; set; }

        public DateTime EffectiveFrom { get; set; }
    }
}
