import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import Employees from '@/services/Employees/employees';

const useEmpDataStore = create(
  persist(
    (set, get) => ({
      // Employee data state
      currentEmployee: null,
      loading: false,
      error: null,

      // Actions
      setCurrentEmployee: (employee) => set({ currentEmployee: employee }),
      
      setLoading: (loading) => set({ loading }),
      
      setError: (error) => set({ error }),

      // Fetch employee data by ID
      fetchEmployeeById: async (empId) => {
        if (!empId) {
          set({ error: 'Employee ID is required' });
          return null;
        }

        set({ loading: true, error: null });
        
        try {
          const employeeData = await Employees.getEmployeeById(empId);
          
          if (employeeData) {
            set({ 
              currentEmployee: employeeData, 
              loading: false, 
              error: null 
            });
            return employeeData;
          } else {
            set({ 
              currentEmployee: null, 
              loading: false, 
              error: 'Employee not found' 
            });
            return null;
          }
        } catch (error) {
          console.error('Error fetching employee data:', error);
          set({ 
            currentEmployee: null, 
            loading: false, 
            error: error.message || 'Failed to fetch employee data' 
          });
          return null;
        }
      },

      // Get employee full name
      getEmployeeFullName: () => {
        const { currentEmployee } = get();
        if (!currentEmployee) return null;
        
        const firstName = currentEmployee.FirstName || '';
        const lastName = currentEmployee.LastName || '';
        return `${firstName} ${lastName}`.trim();
      },

      // Get employee display name (for UI)
      getEmployeeDisplayName: () => {
        const fullName = get().getEmployeeFullName();
        return fullName || 'Employee';
      },

      // Clear employee data
      clearEmployeeData: () => set({ 
        currentEmployee: null, 
        loading: false, 
        error: null 
      }),

      // Update employee data (for when employee updates their profile)
      updateCurrentEmployee: (updatedData) => set((state) => ({
        currentEmployee: state.currentEmployee ? {
          ...state.currentEmployee,
          ...updatedData
        } : null
      }))
    }),
    {
      name: 'emp-data-storage', // unique name for localStorage
      partialize: (state) => ({ 
        currentEmployee: state.currentEmployee 
      }), // only persist currentEmployee data
    }
  )
);

export default useEmpDataStore;