﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EMS.Data;
using EMS.Models;

namespace EMS.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmpPayrollsController : ControllerBase
    {
        private readonly EMSDbContext _context;

        public EmpPayrollsController(EMSDbContext context)
        {
            _context = context;
        }

        // GET: api/EmpPayrolls
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EmpPayroll>>> GetEmpPayrolls()
        {
            return await _context.EmpPayrolls.ToListAsync();
        }

        // GET: api/EmpPayrolls/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EmpPayroll>> GetEmpPayroll(int id)
        {
            var empPayroll = await _context.EmpPayrolls.FindAsync(id);

            if (empPayroll == null)
            {
                return NotFound();
            }

            return empPayroll;
        }

        // PUT: api/EmpPayrolls/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutEmpPayroll(int id, EmpPayroll empPayroll)
        {
            if (id != empPayroll.EmpPayrollID)
            {
                return BadRequest();
            }

            _context.Entry(empPayroll).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmpPayrollExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/EmpPayrolls
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<EmpPayroll>> PostEmpPayroll(EmpPayroll empPayroll)
        {
            _context.EmpPayrolls.Add(empPayroll);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetEmpPayroll", new { id = empPayroll.EmpPayrollID }, empPayroll);
        }

        // DELETE: api/EmpPayrolls/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEmpPayroll(int id)
        {
            var empPayroll = await _context.EmpPayrolls.FindAsync(id);
            if (empPayroll == null)
            {
                return NotFound();
            }

            _context.EmpPayrolls.Remove(empPayroll);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EmpPayrollExists(int id)
        {
            return _context.EmpPayrolls.Any(e => e.EmpPayrollID == id);
        }
    }
}
