import {useState,useEffect} from "react";
import { motion as M, AnimatePresence } from "framer-motion";
import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";
// import MainContent from "@/components/MainContent";
import Footer from "@/components/Footer";

//menu items icons
import { FaUserCheck, FaHandHoldingUsd } from "react-icons/fa";
import {
  Users, UserPlus, Upload, Building, Laptop,
  Calculator, BarChart3, FileText, DollarSign, TrendingUp,
  Shield, Settings, ClipboardList, FileSpreadsheet, MapPin,
  User
} from "lucide-react";
import { Outlet, useLocation } from "react-router-dom";


const InnerLayout = () => {
  // Initialize sidebar state based on screen size
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // Check if we're on desktop (lg breakpoint and above)
    return typeof window !== 'undefined' && window.innerWidth >= 1024;
  });
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("");

  useEffect(() => {
    const currentPath = location.pathname.split("/")[2]; // e.g., "add-employee" from "/dashboard/add-employee"
    setActiveTab(currentPath || "dashboard");
  }, [location]);


  // Handle window resize to manage sidebar state
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 1024;
      // Only auto-open on desktop if it's currently closed
      // Don't auto-close on mobile since user might want to keep it open
      if (isDesktop && !sidebarOpen) {
        setSidebarOpen(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [sidebarOpen]);

  const menuItems = [
    // Employee Management Section
    { id: "employee-management", name: "Employee Management", type: "heading" },
    { id: "all-employees", name: "All Employees", icon: <Users size={18} />, type: "item" },
    { id: "add-employee", name: "Add Employee", icon: <UserPlus size={18} />, type: "item" },
    { id: "import-employees", name: "Import Employees", icon: <Upload size={18} />, type: "item" },
    { id: "onboarding", name: "Onboarding", icon: <FaUserCheck />, type: "item" },

    // Payroll Management Section
    { id: "payroll-management", name: "Payroll Management", type: "heading" },
    { id: "process-payroll", name: "Process Payroll", icon: <Calculator size={18} />, type: "item" },
    { id: "payslips", name: "Payslips", icon: <FileText size={18} />, type: "item" },
    { id: "reports", name: "Reports", icon: <TrendingUp size={18} />, type: "item" },
    { id: "payroll-assignment", name: "Payroll Assignment", icon: <FaHandHoldingUsd />, type: "item" },
    { id: "ctc-assignment", name: "CTC Assignment", icon: <FaHandHoldingUsd size={18} />, type: "item" },

    // Loan Management Section
    { id: "loan-management", name: "Loan Management", type: "heading" },
    { id: "loans-advances", name: "Create Loan & Advance", icon: <DollarSign size={18} />, type: "item" },
    { id: "loans-advances-view", name: "All Loans & Advances", icon: <FaHandHoldingUsd />, type: "item" },

    // Administration Section
    { id: "administration", name: "Administration", type: "heading" },
    { id: "user-roles", name: "User Roles", icon: <Shield size={18} />, type: "item" },
    { id: "settings", name: "System Settings", icon: <Settings size={18} />, type: "item" },
    { id: "audit-logs", name: "Audit Logs", icon: <ClipboardList size={18} />, type: "item" },

    // Masters Section
    { id: "masters", name: "Masters", type: "heading" },
    { id: "departments", name: "Departments", icon: <Building size={18} />, type: "item" },
    { id: "positions", name: "Positions", icon: <MapPin size={18} />, type: "item" },
    { id: "salary-structures", name: "Salary Structures", icon: <BarChart3 size={18} />, type: "item" },
    { id: "assets", name: "Assets", icon: <Laptop size={18} />, type: "item" },

    //Demo Pages
    { id: "demo", name: "Demo Pages (Dev)", type: "heading" },
    { id: "excel-upload", name: "Excel Import Demo", icon: <FileSpreadsheet size={18} />, type: "item" },
    { id: "emp-data-map", name: "Employee Data Mapping", icon: <User size={18} />, type: "item" },
  ];

  const getActiveTabTitle = () => {
    const item = menuItems.find(
      (item) => item.id === activeTab && item.type === "item"
    );
    return item ? item.name : activeTab === "dashboard" ? "Dashboard" : "Page Not Found";
  };


return (
    <div className="h-screen w-full flex overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      
      {/* Sidebar Container */}
      <div className="h-full overflow-y-auto z-50">
        <Sidebar
          menuItems={menuItems}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
        />
      </div>

      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <M.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Main Content Container */}
      <div className="flex-1 flex flex-col h-full overflow-hidden bg-slate-100">
        <Navbar
          getActiveTabTitle={getActiveTabTitle}
          setSidebarOpen={setSidebarOpen}
          sidebarOpen={sidebarOpen}
        />

        <M.main
          className="flex-1 p-4 m-4 bg-white rounded-lg shadow-lg overflow-y-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Outlet />
        </M.main>

        <Footer />
      </div>
    </div>
  );
};

export default InnerLayout;
