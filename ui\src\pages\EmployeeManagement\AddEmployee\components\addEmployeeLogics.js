// addEmployeeLogic.js
import notification from '@/services/NotificationService';

const notify = notification();
// Step configuration

export const stepConfig = {
  1: { 
    title: 'Basic Information',
    icon: 'fas fa-user',
    fields: [
      { name: 'firstName', weight: 2 },
      { name: 'lastName', weight: 2 },
      { name: 'email', weight: 2 },
      { name: 'phoneNumber', weight: 2 },
      { name: 'alternatePhoneNumber', weight: 1 },
      { name: 'dateOfBirth', weight: 2 },
      { name: 'gender', weight: 1 },
      { name: 'maritalStatus', weight: 1 },
      { name: 'address', weight: 2 },
      { name: 'city', weight: 1 },
      { name: 'state', weight: 1 },
      { name: 'pincode', weight: 1 },
      { name: 'emergencyContactName', weight: 2 },
      { name: 'emergencyContactPhone', weight: 2 },
      { name: 'emergencyContactRelation', weight: 1 },
    ],
  },
  2: {
    title: 'Posting Details',
    icon: 'fas fa-briefcase',
    fields: [
      { name: 'department', weight: 2 },
      { name: 'position', weight: 2 },
      { name: 'doj', weight: 2 },
      { name: 'obsStatus', weight: 1 },
      { name: 'isActive', weight: 1 },
    ],
  },
  3: {
    title: 'Bank Details',
    icon: 'fas fa-university',
    fields: [
      { name: 'bankName', weight: 2 },
      { name: 'accountNumber', weight: 2 },
      { name: 'ifscCode', weight: 2 },
      { name: 'branchName', weight: 2 },
      { name: 'cancelledCheque', weight: 2 },
    ],
  },
  4: {
    title: 'Academic Details',
    icon: 'fas fa-graduation-cap',
    fields: [
      { name: 'degree', weight: 2.5 },
      { name: 'institution', weight: 2.5 },
      { name: 'yearOfPassing', weight: 2.5 },
      { name: 'grade', weight: 2.5 },
    ],
  },
  5: {
    title: 'Documents Upload',
    icon: 'fas fa-file-upload',
    fields: [
      { name: 'aadharFile', weight: 2 },
      { name: 'aadharDocNumber', weight: 1 },
      { name: 'panFile', weight: 1 },
      { name: 'panDocNumber', weight: 0.5 },
      { name: 'highSchoolFile', weight: 2 },
      { name: 'highSchoolDocNumber', weight: 1 },
      { name: 'intermediateFile', weight: 1 },
      { name: 'intermediateDocNumber', weight: 0.5 },
      { name: 'graduationFile', weight: 1 },
      { name: 'graduationDocNumber', weight: 0.5 },
      { name: 'postGraduationFile', weight: 1 },
      { name: 'postGraduationDocNumber', weight: 0.5 },
    ],
  }
};

// Initial form data
export const initialFormData = {
  firstName: '',
  // middleName: '',
  lastName: '',
  personalEmail: '',
  workEmail: '',
  phoneNumber: '',
  address: '',
  emergencyContact: '',
  emergencyContactNumber: '',
  dob: '',
  gender: '',
  doj: '',
  obsStatus: 'pending', // Default value
  isActive: false, // Default value
  profilePicture: null,
  bankName: '',
  accountNumber: '',
  ifscCode: '',
  branchName: '',
  cancelledCheque: null,
  degree: '',
  institution: '',
  yearOfPassing: '',
  grade: '',
  department: '',
  position: '',
};

// Calculate completion percentage
export const calculateCompletion = (formData) => {
  let step1Weight = 0, step1Completed = 0;
  let step2Weight = 0, step2Completed = 0;
  let step3Weight = 0, step3Completed = 0;
  let step4Weight = 0, step4Completed = 0;
  let step5Weight = 0, step5Completed = 0;

  // Calculate each step separately
  Object.entries(stepConfig).forEach(([stepNum, step]) => {
    step.fields.forEach((field) => {
      let isCompleted = false;

      const fieldValue = formData[field.name];
      if (fieldValue !== null && fieldValue !== undefined) {
        if (typeof fieldValue === 'string') {
          isCompleted = fieldValue.trim() !== '';
        } else if (fieldValue instanceof File) {
          isCompleted = true;
        } else if (typeof fieldValue === 'boolean') {
          isCompleted = true;
        } else if (typeof fieldValue === 'number') {
          isCompleted = true;
        } else {
          isCompleted = true;
        }
      }

      // Add to appropriate step totals
      if (stepNum === '1') {
        step1Weight += field.weight;
        if (isCompleted) step1Completed += field.weight;
      } else if (stepNum === '2') {
        step2Weight += field.weight;
        if (isCompleted) step2Completed += field.weight;
      } else if (stepNum === '3') {
        step3Weight += field.weight;
        if (isCompleted) step3Completed += field.weight;
      } else if (stepNum === '4') {
        step4Weight += field.weight;
        if (isCompleted) step4Completed += field.weight;
      } else if (stepNum === '5') {
        step5Weight += field.weight;
        if (isCompleted) step5Completed += field.weight;
      }
    });
  });

  // Calculate step percentages
  const step1Percentage = step1Weight > 0 ? (step1Completed / step1Weight) * 100 : 0;
  const step2Percentage = step2Weight > 0 ? (step2Completed / step2Weight) * 100 : 0;
  const step3Percentage = step3Weight > 0 ? (step3Completed / step3Weight) * 100 : 0;
  const step4Percentage = step4Weight > 0 ? (step4Completed / step4Weight) * 100 : 0;
  const step5Percentage = step5Weight > 0 ? (step5Completed / step5Weight) * 100 : 0;

  // Steps 1 and 2 together make up 50% of total progress (25% each)
  const step1Contribution = step1Percentage * 0.25;
  const step2Contribution = step2Percentage * 0.25;

  // Steps 3, 4, and 5 each make up 16.67% of total progress (50% / 3)
  const step3Contribution = step3Percentage * 0.1667;
  const step4Contribution = step4Percentage * 0.1667;
  const step5Contribution = step5Percentage * 0.1666; // Slightly less to avoid rounding issues

  const totalPercentage = step1Contribution + step2Contribution + step3Contribution + step4Contribution + step5Contribution;

  return Math.round(totalPercentage);
};

// Handle input changes
export const handleInputChange = (e, setFormData, setErrors, errors) => {
  const { name, value, type, checked, files } = e.target;
  setFormData((prev) => ({
    ...prev,
    [name]: type === 'checkbox' ? checked : type === 'file' ? files[0] : value,
  }));

  if (errors[name]) {
    setErrors((prev) => ({
      ...prev,
      [name]: '',
    }));
  }
};



// Validate current step
export const validateCurrentStep = (currentStep, formData, setErrors) => {
  const newErrors = {};

  // Validate required fields based on current step
  if (currentStep === 1) {
    // Required fields validation
    if (!formData.firstName?.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.phoneNumber?.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^\d{10}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Phone number must be exactly 10 digits';
    }

    if (!formData.dob?.trim()) {
      newErrors.dob = 'Date of birth is required';
    }

    if (!formData.gender?.trim()) {
      newErrors.gender = 'Gender is required';
    }

    if (!formData.address?.trim()) {
      newErrors.address = 'Address is required';
    }

    // EmergencyContactNumber optional but validate format if provided
    if (formData.emergencyContactNumber && formData.emergencyContactNumber.trim() !== '') {
      if (!/^\d{10}$/.test(formData.emergencyContactNumber)) {
        newErrors.emergencyContactNumber = 'Emergency contact number must be exactly 10 digits';
      }
    }
  }
  else if (currentStep === 2) {
    // Posting Details validations
    if (!formData.department?.trim()) {
      newErrors.department = 'Department is required';
    }
    if (!formData.position?.trim()) {
      newErrors.position = 'Position is required';
    }
  }
   else if (currentStep === 3) {
    // Bank Details validations
    if (!formData.bankName?.trim()) {
      newErrors.bankName = 'Bank name is required';
    }
    if (!formData.accountNumber?.trim()) {
      newErrors.accountNumber = 'Account number is required';
    }
    if (!formData.ifscCode?.trim()) {
      newErrors.ifscCode = 'IFSC code is required';
    }
    if (!formData.branchName?.trim()) {
      newErrors.branchName = 'Branch name is required';
    }
    if (!formData.cancelledCheque || !(formData.cancelledCheque instanceof File)) {
      newErrors.cancelledCheque = 'Cancelled cheque file is required';
    }
  } 
  else if (currentStep === 4) {
    // Academic Details validations
    if (!formData.degree?.trim()) {
      newErrors.degree = 'Degree is required';
    }
    if (!formData.institution?.trim()) {
      newErrors.institution = 'Institution is required';
    }
    if (!formData.yearOfPassing?.trim()) {
      newErrors.yearOfPassing = 'Year of passing is required';
    }
    if (!formData.grade?.trim()) {
      newErrors.grade = 'Grade is required';
    }
  }
  else if (currentStep === 5) {
    // Documents validations - only required documents
    if (!formData.aadharDocNumber?.trim()) {
      newErrors.aadharDocNumber = 'Aadhar number is required';
    }
    if (!formData.aadharFile || !(formData.aadharFile instanceof File)) {
      newErrors.aadharFile = 'Aadhar document is required';
    }
    if (!formData.highSchoolDocNumber?.trim()) {
      newErrors.highSchoolDocNumber = 'High School certificate number is required';
    }
    if (!formData.highSchoolFile || !(formData.highSchoolFile instanceof File)) {
      newErrors.highSchoolFile = 'High School certificate is required';
    }
  } 
  

  setErrors(newErrors);
  const isValid = Object.keys(newErrors).length === 0;

  if (!isValid) {
    notify.error('Please fill in all required fields!');
  }

  return isValid;
};

// Handle next step
export const nextStep = (currentStep, formData, setErrors, setCurrentStep) => {
  if (validateCurrentStep(currentStep, formData, setErrors)) {
    setCurrentStep((prev) => Math.min(prev + 1, 4));
    notify.success(`Step ${currentStep} completed successfully!`);
  }
};

// Handle previous step
export const prevStep = (setCurrentStep) => {
  setCurrentStep((prev) => Math.max(prev - 1, 1));
};

// Handle form submission
export const handleSubmit = async (e, currentStep, formData, setErrors, setCurrentStep, setIsSubmitting, setFormData) => {
  e.preventDefault();

  if (!validateCurrentStep(currentStep, formData, setErrors)) return;

  // If not on final step, just go to the next step
  if (currentStep < 5) {
    nextStep(currentStep, formData, setErrors, setCurrentStep);
    return;
  }

  // Final step submission
  setIsSubmitting(true);
  notify.loading('Submitting employee information...');

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    notify.success('Employee registered successfully!');

    // Optional: Reset form or redirect
    setFormData(initialFormData);
    setCurrentStep(1);

  } catch (error) {
    notify.error('Failed to submit employee information. Please try again.');
  } finally {
    setIsSubmitting(false);
  }
};
